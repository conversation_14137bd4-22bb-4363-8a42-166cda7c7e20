# 📁 คู่มือการจัดการ Path ในโปรเจค

## 🎯 วัตถุประสงค์
แก้ปัญหาการใช้ Hard-coded Path ที่ทำให้ต้องแก้ไขไฟล์หลายไฟล์เมื่อเปลี่ยนชื่อโฟลเดอร์ root

## 🔧 วิธีการแก้ไข

### 1. ไฟล์ Config หลัก
ไฟล์ `config/config.php` เป็นไฟล์กลางสำหรับจัดการ path ทั้งหมด

**เมื่อเปลี่ยนชื่อโฟลเดอร์ root ให้แก้ไขเฉพาะบรรทัดนี้:**
```php
define('PROJECT_FOLDER', 'ชื่อโฟลเดอร์ใหม่'); // เปลี่ยนจาก 'pms' เป็นชื่อใหม่
```

### 2. การใช้งานใน PHP Files

#### แทนที่ Hard-coded Path เดิม:
```php
// เดิม (ไม่ควรใช้)
include($_SERVER['DOCUMENT_ROOT'] . '/pms/templated/head.php');
include($_SERVER['DOCUMENT_ROOT'] . '/pms/connection/connect_ticket.php');
```

#### ใช้ Config Path ใหม่:
```php
// ใหม่ (แนะนำ)
require_once($_SERVER['DOCUMENT_ROOT'] . '/app-innovation/config/config.php');
include(TEMPLATE_PATH . '/head.php');
include(CONNECTION_PATH . '/connect_ticket.php');
```

### 3. การใช้งานใน HTML/CSS/JS

#### แทนที่ Hard-coded URL เดิม:
```html
<!-- เดิม (ไม่ควรใช้) -->
<link rel="stylesheet" href="http://localhost/pms/code/dist/css/adminlte.min.css">
<img src="http://localhost/pms/img/logo.png">
<a href="http://localhost/pms/index.php">Home</a>
```

#### ใช้ Config URL ใหม่:
```html
<!-- ใหม่ (แนะนำ) -->
<link rel="stylesheet" href="<?php echo CODE_URL; ?>/dist/css/adminlte.min.css">
<img src="<?php echo IMG_URL; ?>/logo.png">
<a href="<?php echo BASE_URL; ?>/index.php">Home</a>
```

## 📋 Constants ที่สามารถใช้ได้

### Path Constants (สำหรับ include/require)
- `DOCUMENT_ROOT_PATH` - Path หลักของโปรเจค
- `TEMPLATE_PATH` - Path ของโฟลเดอร์ templated
- `CONNECTION_PATH` - Path ของโฟลเดอร์ connection
- `IMG_PATH` - Path ของโฟลเดอร์ img
- `EXAMPLE_PATH` - Path ของโฟลเดอร์ example
- `TEST_PATH` - Path ของโฟลเดอร์ test

### URL Constants (สำหรับ HTML)
- `BASE_URL` - URL หลักของโปรเจค
- `CODE_URL` - URL ของโฟลเดอร์ code
- `IMG_URL` - URL ของโฟลเดอร์ img
- `EXAMPLE_URL` - URL ของโฟลเดอร์ example
- `TEST_URL` - URL ของโฟลเดอร์ test

### Helper Functions
- `getProjectPath($path)` - สร้าง absolute path
- `getProjectUrl($path)` - สร้าง absolute URL
- `includeFile($relativePath)` - include ไฟล์อย่างปลอดภัย
- `requireFile($relativePath)` - require ไฟล์อย่างปลอดภัย

## 🚀 ตัวอย่างการใช้งาน

### ในไฟล์ PHP:
```php
<?php
// โหลด config (ทำครั้งเดียวต่อไฟล์)
require_once($_SERVER['DOCUMENT_ROOT'] . '/app-innovation/config/config.php');

// Include templates
include(TEMPLATE_PATH . '/head.php');
include(TEMPLATE_PATH . '/menu.php');

// Include database connection
include(CONNECTION_PATH . '/connect_ticket.php');

// สร้าง path สำหรับ upload
$uploadPath = EXAMPLE_PATH . '/uploads/';
$uploadUrl = EXAMPLE_URL . '/uploads/';
?>
```

### ในไฟล์ HTML:
```html
<!-- CSS -->
<link rel="stylesheet" href="<?php echo CODE_URL; ?>/dist/css/adminlte.min.css">

<!-- Images -->
<img src="<?php echo IMG_URL; ?>/logo.png" alt="Logo">

<!-- Links -->
<a href="<?php echo BASE_URL; ?>/index.php">หน้าหลัก</a>

<!-- JavaScript -->
<script src="<?php echo CODE_URL; ?>/plugins/jquery/jquery.min.js"></script>
```

## ✅ ข้อดีของวิธีนี้

1. **แก้ไขครั้งเดียว** - เปลี่ยนชื่อโฟลเดอร์แค่แก้ไขไฟล์ config.php เท่านั้น
2. **ปลอดภัย** - ตรวจสอบไฟล์ก่อน include
3. **ยืดหยุ่น** - รองรับการเปลี่ยน domain หรือ subdirectory
4. **ง่ายต่อการบำรุงรักษา** - จัดการ path ได้จากที่เดียว
5. **รองรับ HTTPS** - ตรวจสอบ protocol อัตโนมัติ

## 🔄 การย้ายโปรเจคไปเซิร์ฟเวอร์ใหม่

เมื่อต้องการย้ายโปรเจคไปเซิร์ฟเวอร์ใหม่:

1. คัดลอกไฟล์ทั้งหมดไปยังเซิร์ฟเวอร์ใหม่
2. แก้ไขเฉพาะไฟล์ `config/config.php` บรรทัด:
   ```php
   define('PROJECT_FOLDER', 'ชื่อโฟลเดอร์ใหม่');
   ```
3. เสร็จสิ้น! ไม่ต้องแก้ไขไฟล์อื่น

## 🛠️ การแก้ไขเพิ่มเติม

หากมีไฟล์อื่นที่ยังใช้ hard-coded path อยู่ ให้:

1. เพิ่ม config ที่ต้นไฟล์:
   ```php
   require_once($_SERVER['DOCUMENT_ROOT'] . '/app-innovation/config/config.php');
   ```

2. แทนที่ hard-coded path ด้วย constants ที่เหมาะสม

3. ทดสอบการทำงาน
