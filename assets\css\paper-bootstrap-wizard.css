.has-error .form-control, .form-control.error {
  background-color: #FFC0A4;
  color: #EB5E28;
  border-color: #EB5E28;
}
.has-success .form-control, .form-control.valid {
  color: #66615b;
  border-color: #e8e7e3;
}
.has-error .form-control:focus, .form-control.error:focus {
  background-color: #FFFFFF;
  border-color: #EB5E28;
}
.has-success .form-control:focus, .form-control.valid:focus {
  background-color: #FFFFFF;
  border-color: #7AC29A;
}
.form-control + .form-control-feedback {
  border-radius: 6px;
  font-size: 14px;
  margin-top: -7px;
  position: absolute;
  right: 10px;
  top: 50%;
  vertical-align: middle;
}

.open .form-control {
  border-bottom-color: transparent;
}

.form-control.input-no-border {
  border: 0 none;
}

.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
  background-color: #E3E3E3;
  cursor: not-allowed;
  color: #9A9A9A;
  opacity: 1;
  filter: alpha(opacity=100);
}

.image-container {
  min-height: 100vh;
  background-position: center center;
  background-size: cover;
}

.wizard-container {
  z-index: 3;
}

/*Navigation*/
.nav-pills {
  background-color: #F3F2EE;
  position: absolute;
  width: 100%;
  height: 4px;
  top: 40px;
  text-align: center;
}
.nav-pills > li + li {
  margin-left: 0;
}
.nav-pills > li > a {
  padding: 0;
  max-width: 70px;
  margin: 0 auto;
  color: rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  position: relative;
  top: -32px;
}
.nav-pills > li > a:after {
  content: '';
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: inline-block;
  position: absolute;
  right: -1px;
  top: -4px;
  transform: scale(0);
  transition: .2s all linear;
}
.nav-pills > li > a:hover, .nav-pills > li > a:focus {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.2);
  outline: 0 !important;
  cursor: pointer;
}
.nav-pills > li.active > a:after,
.nav-pills > li a.active:after {
  content: '';
  border-radius: 50%;
  display: inline-block;
  position: absolute;
  right: 0%;
  width: 70px;
  left: -15px;
  top: -86px;
  -webkit-transform: scale(1);
  -moz-transform: scale(1);
  -o-transition: scale(1);
  -ms-transform: scale(1);
  transform: scale(1);
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
  transition: all 0.2s linear;
}

.nav-pills > li:last-child a.active:after {
  left: -6px;
}

.nav-pills > li.active > a,
.nav-pills > li.active > a:hover,
.nav-pills > li.active > a:focus,

.nav-pills > li a.active,
.nav-pills > li a.active:hover,
.nav-pills > li a.active:focus{
  background-color: transparent;
  font-size: 15px;
  -webkit-transition: font-size 0.2s linear;
  -moz-transition: font-size 0.2s linear;
  -o-transition: font-size 0.2s linear;
  -ms-transition: font-size 0.2s linear;
  transition: font-size 0.2s linear;
}
.nav-pills > li.active > a [class*="ti-"],
.nav-pills > li.active > a:hover [class*="ti-"],
.nav-pills > li.active > a:focus [class*="ti-"],

.nav-pills > li a.active [class*="ti-"],
.nav-pills > li a.active:hover [class*="ti-"],
.nav-pills > li a.active:focus [class*="ti-"]{
  color: #FFFFFF;
  font-size: 24px;
  top: 21px;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
  transition: all 0.2s linear;
  background: transparent;
}

.btn-tooltip {
  white-space: nowrap;
}

.buttons-with-margin .btn {
  margin-bottom: 5px;
}

.label {
  padding: 3px 8px;
  border-radius: 12px;
  color: #FFFFFF;
  font-weight: 500;
  font-size: 0.75em;
  text-transform: uppercase;
  display: inline-block;
  line-height: 1.5em;
}

.label-icon {
  padding: 0.4em 0.55em;
}
.label-icon i {
  font-size: 0.8em;
  line-height: 1;
}

.label-default {
  background-color: #66615b;
}

.label-primary {
  background-color: #7A9E9F;
}

.label-info {
  background-color: #68B3C8;
}

.label-success {
  background-color: #7AC29A;
}

.label-warning {
  background-color: #F3BB45;
}

.label-danger {
  background-color: #EB5E28;
}

.card .card-checkboxes {
  background-color: #F3F2EE;
  box-shadow: none;
  color: rgba(0, 0, 0, 0.3);
}
.card .card-hover-effect {
  -webkit-transition: transform 300ms cubic-bezier(0.34, 2, 0.6, 1), box-shadow 200ms ease;
  -moz-transition: transform 300ms cubic-bezier(0.34, 2, 0.6, 1), box-shadow 200ms ease;
  -o-transition: transform 300ms cubic-bezier(0.34, 2, 0.6, 1), box-shadow 200ms ease;
  -ms-transition: transform 300ms cubic-bezier(0.34, 2, 0.6, 1), box-shadow 200ms ease;
  transition: transform 300ms cubic-bezier(0.34, 2, 0.6, 1), box-shadow 200ms ease;
}
.card .card-hover-effect:hover {
  box-shadow: 0px 12px 17px -7px rgba(0, 0, 0, 0.3);
  -webkit-transform: translateY(-10px);
  -moz-transform: translateY(-10px);
  -o-transition: translateY(-10px);
  -ms-transform: translateY(-10px);
  transform: translateY(-10px);
}

.wizard-card {
  min-height: 410px;
  border: none !important;
}

.wizard-card ul li {
  position: relative;
}
.wizard-card .picture-container {
  position: relative;
  cursor: pointer;
  text-align: center !important;
}
.wizard-card .icon-circle {
  font-size: 20px;
  border: 3px solid #5c5c5c;
  text-align: center;
  border-radius: 50%;
  color: #5c5c5c;
  font-weight: 600;
  width: 70px;
  height: 70px;
  background-color: #FFFFFF;
  margin: 0 auto;
  position: relative;
  top: -2px;
  overflow: hidden;
}
.wizard-card .icon-circle [class*="ti-"] {
  position: absolute;
  z-index: 500;
  left: -3px;
  right: 0px;
  width: 70px;
  text-align: center;
  top: 23px;
  background: #fff;
}
.wizard-card span.icon-text {
  width: 70px;
  height: auto;
  display: block;
  margin: 0 auto;
  text-align: center;
}
.wizard-card .picture {
  width: 106px;
  height: 106px;
  background-color: #d8d1c9;
  border: 4px solid transparent;
  color: #FFFFFF;
  border-radius: 50%;
  margin: 5px auto;
  overflow: hidden;
  transition: all 0.2s;
  -webkit-transition: all 0.2s;
}
.wizard-card .picture:hover {
  border-color: #2ca8ff;
}
.wizard-card .picture-src {
  width: 100%;
}
.wizard-card[data-color="azure"] .picture:hover {
  border-color: #7A9E9F;
}
.wizard-card[data-color="azure"] .nav-pills > li.active > a:after,
.wizard-card[data-color="azure"] .nav-pills > li a.active:after {
  background-color: #7A9E9F;
}
.wizard-card[data-color="azure"] .nav-pills > li.active > a,
.wizard-card[data-color="azure"] .nav-pills > li a.active {
  color: #7A9E9F;
}
.wizard-card[data-color="azure"] .nav-pills .icon-circle.checked {
  border-color: #7A9E9F;
}
.wizard-card[data-color="azure"] .choice.active .card-checkboxes {
  color: #7A9E9F;
}
.wizard-card[data-color="azure"] .wizard-navigation .progress-bar {
  background-color: #7A9E9F;
  height: 100%;
}
.wizard-card[data-color="green"] .picture:hover {
  border-color: #7AC29A;
}
.wizard-card[data-color="green"] .nav-pills > li.active > a:after,
.wizard-card[data-color="green"] .nav-pills > li a.active:after {
  background-color: #7AC29A;
}
.wizard-card[data-color="green"] .nav-pills > li.active > a,
.wizard-card[data-color="green"] .nav-pills > li a.active {
  color: #7AC29A;
}
.wizard-card[data-color="green"] .nav-pills .icon-circle.checked {
  border-color: #7AC29A;
}
.wizard-card[data-color="green"] .choice.active .card-checkboxes {
  color: #7AC29A;
}
.wizard-card[data-color="green"] .wizard-navigation .progress-bar {
  background-color: #7AC29A;
  height: 100%;
}
.wizard-card[data-color="blue"] .picture:hover {
  border-color: #68B3C8;
}
.wizard-card[data-color="blue"] .nav-pills > li.active > a:after,
.wizard-card[data-color="blue"] .nav-pills > li a.active:after {
  background-color: #68B3C8;
}
.wizard-card[data-color="blue"] .nav-pills > li.active > a,
.wizard-card[data-color="blue"] .nav-pills > li a.active {
  color: #68B3C8;
}
.wizard-card[data-color="blue"] .nav-pills .icon-circle.checked {
  border-color: #68B3C8;
}
.wizard-card[data-color="blue"] .choice.active .card-checkboxes {
  color: #68B3C8;
}
.wizard-card[data-color="blue"] .wizard-navigation .progress-bar {
  background-color: #68B3C8;
  height: 100%;
}
.wizard-card[data-color="orange"] .picture:hover {
  border-color: #F3BB45;
}
.wizard-card[data-color="orange"] .nav-pills > li.active > a:after,
.wizard-card[data-color="orange"] .nav-pills > li a.active:after {
  background-color: #F3BB45;
}
.wizard-card[data-color="orange"] .nav-pills > li.active > a,
.wizard-card[data-color="orange"] .nav-pills > li a.active {
  color: #F3BB45;
}
.wizard-card[data-color="orange"] .nav-pills .icon-circle.checked {
  border-color: #F3BB45;
}
.wizard-card[data-color="orange"] .choice.active .card-checkboxes {
  color: #F3BB45;
}
.wizard-card[data-color="orange"] .wizard-navigation .progress-bar {
  background-color: #F3BB45;
  height: 100%;
}
.wizard-card[data-color="theme"] .picture:hover {
  border-color: #0D47A1;
}
.wizard-card[data-color="theme"] .nav-pills > li.active > a:after,
.wizard-card[data-color="theme"] .nav-pills > li a.active:after {
  background-color: #0D47A1;
}
.wizard-card[data-color="theme"] .nav-pills > li.active > a,
.wizard-card[data-color="theme"] .nav-pills > li a.active {
  color: #0D47A1;
}
.wizard-card[data-color="theme"] .nav-pills .icon-circle.checked {
  border-color: #0D47A1;
}
.wizard-card[data-color="theme"] .choice.active .card-checkboxes {
  color: #0D47A1;
}
.wizard-card[data-color="theme"] .wizard-navigation .progress-bar {
  background-color: #0D47A1;
  height: 100%;
}
.wizard-card[data-color="red"] .picture:hover {
  border-color: #EB5E28;
}
.wizard-card[data-color="red"] .nav-pills > li.active > a:after,
.wizard-card[data-color="red"] .nav-pills > li a.active:after {
  background-color: #EB5E28;
}
.wizard-card[data-color="red"] .nav-pills > li.active > a,
.wizard-card[data-color="red"] .nav-pills > li a.active {
  color: #EB5E28;
}
.wizard-card[data-color="red"] .nav-pills .icon-circle.checked {
  border-color: #EB5E28;
}
.wizard-card[data-color="red"] .choice.active .card-checkboxes {
  color: #EB5E28;
}
.wizard-card[data-color="red"] .wizard-navigation .progress-bar {
  background-color: #EB5E28;
  height: 100%;
}
.wizard-card .picture input[type="file"] {
  cursor: pointer;
  display: block;
  height: 100%;
  left: 0;
  opacity: 0 !important;
  position: absolute;
  top: 0;
  width: 100%;
}
.wizard-card .tab-content {
  min-height: 435px;
  padding: 105px 20px 10px;
}
.wizard-card .wizard-footer {
  padding: 0 15px 5px;
}
.wizard-card .disabled {
  display: none;
}
.wizard-card .wizard-header {
  padding: 15px 15px 15px 15px;
  position: relative;
  border-radius: 3px 3px 0 0;
  z-index: 3;
}
.wizard-card .wizard-header h3 {
  text-align: center;
}
.wizard-card .wizard-title {
  color: #252422;
  font-weight: 300;
  margin: 0;
}
.wizard-card .category {
  font-size: 14px;
  font-weight: 400;
  color: #9A9A9A;
  margin-bottom: 0px;
  text-align: center;
}
.wizard-card .wizard-navigation {
  position: relative;
}
.wizard-card .wizard-navigation .progress-with-circle {
  position: relative;
  top: 40px;
  z-index: 50;
  height: 4px;
}
.wizard-card .wizard-navigation .progress-with-circle .progress-bar {
  box-shadow: none;
  -webkit-transition: width .3s ease;
  -o-transition: width .3s ease;
  transition: width .3s ease;
}
.wizard-card .info-text {
  text-align: center;
  padding-bottom: 18px;
  padding-top: 12px;
}
.wizard-card .choice {
  text-align: center;
  cursor: pointer;
  margin-top: 38px;
}
.wizard-card .choice .icon {
  text-align: center;
  vertical-align: middle;
  height: 116px;
  width: 116px;
  border-radius: 50%;
  background-color: #999999;
  color: #FFFFFF;
  margin: 0 auto 20px;
  border: 4px solid #CCCCCC;
  transition: all 0.2s;
  -webkit-transition: all 0.2s;
}
.wizard-card .choice i {
  font-size: 32px;
  line-height: 55px;
}
.wizard-card .choice:hover .icon, .wizard-card .choice.active .icon {
  border-color: #2ca8ff;
}
.wizard-card .choice input[type="radio"],
.wizard-card .choice input[type="checkbox"] {
  position: absolute;
  left: -10000px;
  z-index: -1;
}
.wizard-card .description {
  color: #999999;
  font-size: 14px;
}
