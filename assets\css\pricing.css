/*PRICE COLOR CODE START*/
.generic_content{
	background-color: #fff;
}

 .generic_content .generic_head_price{
	background-color: #f6f6f6;
}

 .generic_content .generic_head_price .generic_head_content .head_bg{
	border-color: #e4e4e4 rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) #e4e4e4;
}

 .generic_content .generic_head_price .generic_head_content .head span{
	color: #525252;
}

 .generic_content .generic_head_price .generic_price_tag .price .sign{
    color: #414141;
}

 .generic_content .generic_head_price .generic_price_tag .price .currency{
    color: #414141;
}

 .generic_content .generic_head_price .generic_price_tag .price .cent{
    color: #414141;
}

 .generic_content .generic_head_price .generic_price_tag .month{
    color: #414141;
}

 .generic_content .generic_feature_list ul li{	
	color: #a7a7a7;
}

 .generic_content .generic_feature_list ul li span{
	color: #414141;
}
 .generic_content .generic_feature_list ul li:hover{
	background-color: #E4E4E4;
	border-left: 5px solid #0D47A1;
}

 .generic_content .generic_price_btn a{
	border: 1px solid #0D47A1; 
    color: #0D47A1;
} 

 .generic_content.active .generic_head_price .generic_head_content .head_bg,
 .generic_content:hover .generic_head_price .generic_head_content .head_bg{
	border-color: #0D47A1 rgba(0, 0, 0, 0) rgba(0, 0, 0, 0) #0D47A1;
	color: #fff;
}

 .generic_content:hover .generic_head_price .generic_head_content .head span,
 .generic_content.active .generic_head_price .generic_head_content .head span{
	color: #fff;
}

 .generic_content:hover .generic_price_btn a,
 .generic_content.active .generic_price_btn a{
	background-color: #0D47A1;
	color: #fff;
}

.row .table{
    padding: 28px 0;
}

/*PRICE BODY CODE START*/

 .generic_content{
	overflow: hidden;
	position: relative;
	text-align: center;
}

 .generic_content .generic_head_price {
	margin: 0 0 20px 0;
}

 .generic_content .generic_head_price .generic_head_content{
	margin: 0 0 50px 0;
}

 .generic_content .generic_head_price .generic_head_content .head_bg{
    border-style: solid;
    border-width: 90px 1411px 23px 399px;
	position: absolute;
}

 .generic_content .generic_head_price .generic_head_content .head{
	padding-top: 40px;
	position: relative;
	z-index: 1;
}

 .generic_content .generic_head_price .generic_head_content .head span{
    font-size: 28px;
    font-weight: 400;
    letter-spacing: 2px;
    margin: 0;
    padding: 0;
    text-transform: uppercase;
}

 .generic_content .generic_head_price .generic_price_tag{
	padding: 0 0 20px;
}

 .generic_content .generic_head_price .generic_price_tag .price{
	display: block;
}

 .generic_content .generic_head_price .generic_price_tag .price .sign{
    display: inline-block;
    font-family: sans-serif;
    font-size: 28px;
    font-weight: 400;
    vertical-align: middle;
}

 .generic_content .generic_head_price .generic_price_tag .price .currency{
    font-family: sans-serif;
    font-size: 60px;
    font-weight: 300;
    letter-spacing: -2px;
    line-height: 60px;
    padding: 0;
    vertical-align: middle;
}

 .generic_content .generic_head_price .generic_price_tag .price .cent{
    display: inline-block;
    font-family: sans-serif;
    font-size: 24px;
    font-weight: 400;
    vertical-align: bottom;
}

 .generic_content .generic_head_price .generic_price_tag .month{
    font-family: sans-serif;
    font-size: 18px;
    font-weight: 400;
    letter-spacing: 3px;
    vertical-align: bottom;
}

 .generic_content .generic_feature_list ul{
	list-style: none;
	padding: 0;
	margin: 0;
}

 .generic_content .generic_feature_list ul li{
	font-size: 18px;
	padding: 15px 0;
	font-weight: 500;
	transition: all 0.3s ease-in-out 0s;
}
 .generic_content .generic_feature_list ul li:hover{
	transition: all 0.3s ease-in-out 0s;
	-moz-transition: all 0.3s ease-in-out 0s;
	-ms-transition: all 0.3s ease-in-out 0s;
	-o-transition: all 0.3s ease-in-out 0s;
	-webkit-transition: all 0.3s ease-in-out 0s;

}
 .generic_content .generic_feature_list ul li .fa{
	padding: 0 10px;
}
 .generic_content .generic_price_btn{
	margin: 20px 0 32px;
}

 .generic_content .generic_price_btn a{
    border-radius: 50px;
	-moz-border-radius: 50px;
	-ms-border-radius: 50px;
	-o-border-radius: 50px;
	-webkit-border-radius: 50px;
    display: inline-block;
    font-size: 18px;
    outline: medium none;
    padding: 12px 30px;
    text-decoration: none;
    text-transform: uppercase;
}

 .generic_content,
 .generic_content:hover,
 .generic_content .generic_head_price .generic_head_content .head_bg,
 .generic_content:hover .generic_head_price .generic_head_content .head_bg,
 .generic_content .generic_head_price .generic_head_content .head h2,
 .generic_content:hover .generic_head_price .generic_head_content .head h2,
 .generic_content .price,
 .generic_content:hover .price,
 .generic_content .generic_price_btn a,
 .generic_content:hover .generic_price_btn a{
	transition: all 0.3s ease-in-out 0s;
	-moz-transition: all 0.3s ease-in-out 0s;
	-ms-transition: all 0.3s ease-in-out 0s;
	-o-transition: all 0.3s ease-in-out 0s;
	-webkit-transition: all 0.3s ease-in-out 0s;
} 
@media (max-width: 320px) {	
}

@media (max-width: 767px) {
	 .generic_content{
		margin-bottom:75px;
	}
}
@media (min-width: 768px) and (max-width: 991px) {
	 .col-md-3{
		float:left;
		width:50%;
	}
	
	 .col-md-4{
		float:left;
		width:50%;
	}
	
	 .generic_content{
		margin-bottom:75px;
	}
}

@media (min-width: 992px) and (max-width: 1199px) {
}
@media (min-width: 1200px) {
}

.text-center h1,
.text-center h1 a{
	color: #7885CB;
	font-size: 30px;
	font-weight: 300;
	text-decoration: none;
}
.demo-pic{
	margin: 0 auto;
}
.demo-pic:hover{
	opacity: 0.7;
}

.price-heading{
    text-align: center;
}
.price-heading h1{
	color: #666;
	margin: 0;
	padding: 0 0 50px 0;
}
.demo-button {
    background-color: #333333;
    color: #ffffff;
    display: table;
    font-size: 20px;
    margin-left: auto;
    margin-right: auto;
    margin-top: 20px;
    margin-bottom: 50px;
    outline-color: -moz-use-text-color;
    outline-style: none;
    outline-width: medium ;
    padding: 10px;
    text-align: center;
    text-transform: uppercase;
}
.bottom_btn{
	background-color: #333333;
    color: #ffffff;
    display: table;
    font-size: 28px;
    margin: 60px auto 20px;
    padding: 10px 25px;
    text-align: center;
    text-transform: uppercase;
}
.demo-button:hover{
	background-color: #666;
	color: #FFF;
	text-decoration:none;
	
}
.bottom_btn:hover{
	background-color: #666;
	color: #FFF;
	text-decoration:none;
}
