<?php
/**
 * Configuration File สำหรับจัดการ Path ของโปรเจค
 * เมื่อเปลี่ยนชื่อโฟลเดอร์ root ให้แก้ไขเฉพาะไฟล์นี้เท่านั้น
 */

// ตรวจสอบว่าไฟล์นี้ถูกเรียกใช้โดยตรงหรือไม่
if (!defined('CONFIG_LOADED')) {
    define('CONFIG_LOADED', true);
}

// กำหนดชื่อโฟลเดอร์ root ของโปรเจค
// เมื่อเปลี่ยนชื่อโฟลเดอร์ ให้แก้ไขเฉพาะบรรทัดนี้
define('PROJECT_FOLDER', 'app-innovation'); // เปลี่ยนจาก 'pms' เป็น 'app-innovation'

// สร้าง Base Path สำหรับใช้งาน
define('BASE_PATH', '/' . PROJECT_FOLDER);
define('DOCUMENT_ROOT_PATH', $_SERVER['DOCUMENT_ROOT'] . BASE_PATH);

// สร้าง URL Base สำหรับใช้ใน HTML
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'];
define('BASE_URL', $protocol . '://' . $host . BASE_PATH);

// Path สำหรับโฟลเดอร์ต่างๆ
define('TEMPLATE_PATH', DOCUMENT_ROOT_PATH . '/templated');
define('CONNECTION_PATH', DOCUMENT_ROOT_PATH . '/connection');
define('IMG_PATH', DOCUMENT_ROOT_PATH . '/img');
define('EXAMPLE_PATH', DOCUMENT_ROOT_PATH . '/example');
define('TEST_PATH', DOCUMENT_ROOT_PATH . '/test');

// URL สำหรับใช้ใน HTML
define('TEMPLATE_URL', BASE_URL . '/templated');
define('IMG_URL', BASE_URL . '/img');
define('EXAMPLE_URL', BASE_URL . '/example');
define('TEST_URL', BASE_URL . '/test');
define('CODE_URL', BASE_URL . '/code');

// ฟังก์ชันช่วยสำหรับสร้าง Path
function getProjectPath($path = '') {
    return DOCUMENT_ROOT_PATH . ($path ? '/' . ltrim($path, '/') : '');
}

function getProjectUrl($path = '') {
    return BASE_URL . ($path ? '/' . ltrim($path, '/') : '');
}

// ฟังก์ชันสำหรับ include ไฟล์
function includeFile($relativePath) {
    $fullPath = DOCUMENT_ROOT_PATH . '/' . ltrim($relativePath, '/');
    if (file_exists($fullPath)) {
        include $fullPath;
    } else {
        throw new Exception("File not found: " . $fullPath);
    }
}

// ฟังก์ชันสำหรับ require ไฟล์
function requireFile($relativePath) {
    $fullPath = DOCUMENT_ROOT_PATH . '/' . ltrim($relativePath, '/');
    if (file_exists($fullPath)) {
        require $fullPath;
    } else {
        throw new Exception("File not found: " . $fullPath);
    }
}

?>
